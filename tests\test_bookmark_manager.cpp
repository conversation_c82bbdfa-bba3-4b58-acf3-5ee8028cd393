#include <QtTest/QtTest>
#include <QSignalSpy>
#include <QTemporaryDir>
#include <QSettings>
#include <QFile>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>

#include "BookmarkManager.h"

class TestBookmarkManager : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Basic CRUD operations
    void testAddBookmark();
    void testAddBookmarkWithValidation();
    void testUpdateBookmark();
    void testRemoveBookmark();
    void testRemoveBookmarksForDocument();
    void testClearAllBookmarks();

    // Bookmark retrieval
    void testGetBookmark();
    void testGetAllBookmarks();
    void testGetBookmarksForDocument();
    void testGetFavoriteBookmarks();
    void testGetRecentBookmarks();
    void testGetMostAccessedBookmarks();

    // Bookmark structure tests
    void testEnhancedBookmarkStructure();
    void testBookmarkSerialization();
    void testBookmarkDeserialization();
    void testBookmarkComparison();
    void testBookmarkIdGeneration();
    void testBookmarkAccessUpdate();

    // Search and filtering
    void testSearchBookmarks();
    void testFilterBookmarks();
    void testSortBookmarks();
    void testGetBookmarksByCategory();
    void testGetBookmarksByTag();

    // Categories and tags
    void testGetAllCategories();
    void testGetAllTags();
    void testGetCategoriesForDocument();
    void testGetTagsForDocument();
    void testMostUsedCategories();
    void testMostUsedTags();

    // Statistics
    void testGetBookmarkCount();
    void testGetBookmarkCountForDocument();
    void testGetLastBookmarkTime();

    // Favorites functionality
    void testToggleFavorite();
    void testSetFavorite();

    // Access tracking
    void testRecordAccess();
    void testAccessCountTracking();
    void testLastAccessedTracking();

    // Import/Export
    void testExportBookmarks();
    void testImportBookmarks();
    void testExportBookmarksForDocument();

    // Persistence
    void testSaveToSettings();
    void testLoadFromSettings();
    void testAutoSaveEnabled();
    void testAutoSaveDisabled();

    // Validation
    void testIsValidBookmark();
    void testBookmarkExists();
    void testHasBookmarkForPage();

    // Cleanup operations
    void testRemoveOrphanedBookmarks();
    void testRemoveOldBookmarks();

    // Configuration
    void testMaxBookmarksConfiguration();
    void testBookmarkTrimming();

    // Signal emission
    void testBookmarkSignals();
    void testFavoriteSignals();
    void testAccessSignals();

    // Edge cases and stress tests
    void testDuplicateBookmarks();
    void testInvalidBookmarkData();
    void testManyBookmarks();
    void testConcurrentAccess();

private:
    BookmarkManager* m_bookmarkManager;
    QTemporaryDir* m_tempDir;
    QString m_testSettingsPath;
    
    EnhancedBookmark createTestBookmark(const QString& name = "Test Bookmark",
                                       const QString& filePath = "/test/document.pdf",
                                       int pageNumber = 0);
    void verifyBookmarkEquality(const EnhancedBookmark& expected, const EnhancedBookmark& actual);
    bool containsBookmarkWithName(const QList<EnhancedBookmark>& bookmarks, const QString& name);
};

void TestBookmarkManager::initTestCase()
{
    // Create temporary directory for test files
    m_tempDir = new QTemporaryDir();
    QVERIFY(m_tempDir->isValid());
    
    m_testSettingsPath = m_tempDir->path() + "/test_settings.ini";
}

void TestBookmarkManager::cleanupTestCase()
{
    delete m_tempDir;
    m_tempDir = nullptr;
}

void TestBookmarkManager::init()
{
    // Create fresh bookmark manager for each test
    QSettings::setPath(QSettings::IniFormat, QSettings::UserScope, m_tempDir->path());
    m_bookmarkManager = new BookmarkManager(this);
    m_bookmarkManager->setAutoSaveEnabled(false); // Disable auto-save for tests
    m_bookmarkManager->clearAllBookmarks();
}

void TestBookmarkManager::cleanup()
{
    delete m_bookmarkManager;
    m_bookmarkManager = nullptr;
    
    // Clean up settings file
    QFile::remove(m_testSettingsPath);
}

void TestBookmarkManager::testAddBookmark()
{
    QSignalSpy spy(m_bookmarkManager, &BookmarkManager::bookmarkAdded);
    
    EnhancedBookmark bookmark = createTestBookmark();
    QString id = m_bookmarkManager->addBookmark(bookmark);
    
    QVERIFY(!id.isEmpty());
    QCOMPARE(spy.count(), 1);
    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 1);
    
    // Verify the bookmark was added correctly
    EnhancedBookmark retrieved = m_bookmarkManager->getBookmark(id);
    QCOMPARE(retrieved.name, bookmark.name);
    QCOMPARE(retrieved.filePath, bookmark.filePath);
    QCOMPARE(retrieved.pageNumber, bookmark.pageNumber);
}

void TestBookmarkManager::testAddBookmarkWithValidation()
{
    // Test adding invalid bookmark
    EnhancedBookmark invalidBookmark;
    invalidBookmark.name = ""; // Invalid: empty name
    invalidBookmark.filePath = ""; // Invalid: empty file path
    
    QString id = m_bookmarkManager->addBookmark(invalidBookmark);
    QVERIFY(id.isEmpty());
    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 0);
    
    // Test adding valid bookmark
    EnhancedBookmark validBookmark = createTestBookmark();
    id = m_bookmarkManager->addBookmark(validBookmark);
    QVERIFY(!id.isEmpty());
    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 1);
}

void TestBookmarkManager::testUpdateBookmark()
{
    QSignalSpy addSpy(m_bookmarkManager, &BookmarkManager::bookmarkAdded);
    QSignalSpy updateSpy(m_bookmarkManager, &BookmarkManager::bookmarkUpdated);
    
    // Add a bookmark
    EnhancedBookmark bookmark = createTestBookmark();
    QString id = m_bookmarkManager->addBookmark(bookmark);
    QVERIFY(!id.isEmpty());
    QCOMPARE(addSpy.count(), 1);
    
    // Update the bookmark
    EnhancedBookmark updatedBookmark = bookmark;
    updatedBookmark.name = "Updated Bookmark";
    updatedBookmark.description = "Updated description";
    updatedBookmark.pageNumber = 5;
    
    bool success = m_bookmarkManager->updateBookmark(id, updatedBookmark);
    QVERIFY(success);
    QCOMPARE(updateSpy.count(), 1);
    
    // Verify the update
    EnhancedBookmark retrieved = m_bookmarkManager->getBookmark(id);
    QCOMPARE(retrieved.name, QString("Updated Bookmark"));
    QCOMPARE(retrieved.description, QString("Updated description"));
    QCOMPARE(retrieved.pageNumber, 5);
    QCOMPARE(retrieved.id, id); // ID should remain the same
}

void TestBookmarkManager::testRemoveBookmark()
{
    QSignalSpy addSpy(m_bookmarkManager, &BookmarkManager::bookmarkAdded);
    QSignalSpy removeSpy(m_bookmarkManager, &BookmarkManager::bookmarkRemoved);
    
    // Add a bookmark
    EnhancedBookmark bookmark = createTestBookmark();
    QString id = m_bookmarkManager->addBookmark(bookmark);
    QVERIFY(!id.isEmpty());
    QCOMPARE(addSpy.count(), 1);
    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 1);
    
    // Remove the bookmark
    bool success = m_bookmarkManager->removeBookmark(id);
    QVERIFY(success);
    QCOMPARE(removeSpy.count(), 1);
    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 0);
    
    // Verify removal
    EnhancedBookmark retrieved = m_bookmarkManager->getBookmark(id);
    QVERIFY(retrieved.id.isEmpty()); // Should return empty bookmark
    
    // Test removing non-existent bookmark
    bool failureResult = m_bookmarkManager->removeBookmark("non-existent-id");
    QVERIFY(!failureResult);
}

void TestBookmarkManager::testRemoveBookmarksForDocument()
{
    // Add bookmarks for different documents
    EnhancedBookmark bookmark1 = createTestBookmark("Bookmark 1", "/test/doc1.pdf", 0);
    EnhancedBookmark bookmark2 = createTestBookmark("Bookmark 2", "/test/doc1.pdf", 1);
    EnhancedBookmark bookmark3 = createTestBookmark("Bookmark 3", "/test/doc2.pdf", 0);
    
    m_bookmarkManager->addBookmark(bookmark1);
    m_bookmarkManager->addBookmark(bookmark2);
    m_bookmarkManager->addBookmark(bookmark3);
    
    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 3);
    
    // Remove bookmarks for doc1.pdf
    bool success = m_bookmarkManager->removeBookmarksForDocument("/test/doc1.pdf");
    QVERIFY(success);
    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 1);
    
    // Verify only doc2.pdf bookmark remains
    QList<EnhancedBookmark> remaining = m_bookmarkManager->getAllBookmarks();
    QCOMPARE(remaining.size(), 1);
    QCOMPARE(remaining.first().filePath, QString("/test/doc2.pdf"));
}

void TestBookmarkManager::testClearAllBookmarks()
{
    QSignalSpy clearSpy(m_bookmarkManager, &BookmarkManager::bookmarksCleared);
    
    // Add some bookmarks
    for (int i = 0; i < 5; ++i) {
        EnhancedBookmark bookmark = createTestBookmark(QString("Bookmark %1").arg(i));
        m_bookmarkManager->addBookmark(bookmark);
    }
    
    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 5);
    
    // Clear all bookmarks
    m_bookmarkManager->clearAllBookmarks();
    
    QCOMPARE(clearSpy.count(), 1);
    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 0);
    QVERIFY(m_bookmarkManager->getAllBookmarks().isEmpty());
}

void TestBookmarkManager::testGetBookmark()
{
    // Test getting non-existent bookmark
    EnhancedBookmark nonExistent = m_bookmarkManager->getBookmark("non-existent-id");
    QVERIFY(nonExistent.id.isEmpty());
    
    // Add a bookmark and retrieve it
    EnhancedBookmark bookmark = createTestBookmark();
    QString id = m_bookmarkManager->addBookmark(bookmark);
    
    EnhancedBookmark retrieved = m_bookmarkManager->getBookmark(id);
    QCOMPARE(retrieved.id, id);
    QCOMPARE(retrieved.name, bookmark.name);
    QCOMPARE(retrieved.filePath, bookmark.filePath);
}

void TestBookmarkManager::testGetAllBookmarks()
{
    // Test empty list
    QList<EnhancedBookmark> empty = m_bookmarkManager->getAllBookmarks();
    QVERIFY(empty.isEmpty());
    
    // Add bookmarks
    QStringList bookmarkNames = {"Bookmark 1", "Bookmark 2", "Bookmark 3"};
    for (const QString& name : bookmarkNames) {
        EnhancedBookmark bookmark = createTestBookmark(name);
        m_bookmarkManager->addBookmark(bookmark);
    }
    
    QList<EnhancedBookmark> all = m_bookmarkManager->getAllBookmarks();
    QCOMPARE(all.size(), 3);
    
    // Verify all bookmarks are present (order might be different)
    for (const QString& name : bookmarkNames) {
        QVERIFY(containsBookmarkWithName(all, name));
    }
}

void TestBookmarkManager::testGetBookmarksForDocument()
{
    // Add bookmarks for different documents
    EnhancedBookmark bookmark1 = createTestBookmark("Doc1 Bookmark 1", "/test/doc1.pdf", 0);
    EnhancedBookmark bookmark2 = createTestBookmark("Doc1 Bookmark 2", "/test/doc1.pdf", 1);
    EnhancedBookmark bookmark3 = createTestBookmark("Doc2 Bookmark 1", "/test/doc2.pdf", 0);
    
    m_bookmarkManager->addBookmark(bookmark1);
    m_bookmarkManager->addBookmark(bookmark2);
    m_bookmarkManager->addBookmark(bookmark3);
    
    // Get bookmarks for doc1.pdf
    QList<EnhancedBookmark> doc1Bookmarks = m_bookmarkManager->getBookmarksForDocument("/test/doc1.pdf");
    QCOMPARE(doc1Bookmarks.size(), 2);
    
    for (const EnhancedBookmark& bookmark : doc1Bookmarks) {
        QCOMPARE(bookmark.filePath, QString("/test/doc1.pdf"));
    }
    
    // Get bookmarks for doc2.pdf
    QList<EnhancedBookmark> doc2Bookmarks = m_bookmarkManager->getBookmarksForDocument("/test/doc2.pdf");
    QCOMPARE(doc2Bookmarks.size(), 1);
    QCOMPARE(doc2Bookmarks.first().filePath, QString("/test/doc2.pdf"));
    
    // Get bookmarks for non-existent document
    QList<EnhancedBookmark> nonExistent = m_bookmarkManager->getBookmarksForDocument("/test/nonexistent.pdf");
    QVERIFY(nonExistent.isEmpty());
}

EnhancedBookmark TestBookmarkManager::createTestBookmark(const QString& name, const QString& filePath, int pageNumber)
{
    EnhancedBookmark bookmark;
    bookmark.name = name;
    bookmark.filePath = filePath;
    bookmark.fileName = QFileInfo(filePath).fileName();
    bookmark.pageNumber = pageNumber;
    bookmark.zoomFactor = 1.0;
    bookmark.rotation = 0;
    bookmark.created = QDateTime::currentDateTime();
    bookmark.lastAccessed = QDateTime::currentDateTime();
    bookmark.accessCount = 0;
    bookmark.color = Qt::blue;
    bookmark.isFavorite = false;
    bookmark.category = "General";
    bookmark.tags = QStringList() << "test" << "sample";
    bookmark.description = "Test bookmark description";
    bookmark.notes = "Test notes";
    return bookmark;
}

void TestBookmarkManager::verifyBookmarkEquality(const EnhancedBookmark& expected, const EnhancedBookmark& actual)
{
    QCOMPARE(actual.name, expected.name);
    QCOMPARE(actual.filePath, expected.filePath);
    QCOMPARE(actual.fileName, expected.fileName);
    QCOMPARE(actual.pageNumber, expected.pageNumber);
    QCOMPARE(actual.zoomFactor, expected.zoomFactor);
    QCOMPARE(actual.rotation, expected.rotation);
    QCOMPARE(actual.color, expected.color);
    QCOMPARE(actual.isFavorite, expected.isFavorite);
    QCOMPARE(actual.category, expected.category);
    QCOMPARE(actual.tags, expected.tags);
    QCOMPARE(actual.description, expected.description);
    QCOMPARE(actual.notes, expected.notes);
}

bool TestBookmarkManager::containsBookmarkWithName(const QList<EnhancedBookmark>& bookmarks, const QString& name)
{
    for (const EnhancedBookmark& bookmark : bookmarks) {
        if (bookmark.name == name) {
            return true;
        }
    }
    return false;
}

void TestBookmarkManager::testGetFavoriteBookmarks()
{
    // Add bookmarks with different favorite status
    EnhancedBookmark bookmark1 = createTestBookmark("Favorite 1");
    bookmark1.isFavorite = true;

    EnhancedBookmark bookmark2 = createTestBookmark("Not Favorite");
    bookmark2.isFavorite = false;

    EnhancedBookmark bookmark3 = createTestBookmark("Favorite 2");
    bookmark3.isFavorite = true;

    m_bookmarkManager->addBookmark(bookmark1);
    m_bookmarkManager->addBookmark(bookmark2);
    m_bookmarkManager->addBookmark(bookmark3);

    QList<EnhancedBookmark> favorites = m_bookmarkManager->getFavoriteBookmarks();
    QCOMPARE(favorites.size(), 2);

    for (const EnhancedBookmark& bookmark : favorites) {
        QVERIFY(bookmark.isFavorite);
    }
}

void TestBookmarkManager::testGetRecentBookmarks()
{
    // Add bookmarks with different access times
    QDateTime now = QDateTime::currentDateTime();

    EnhancedBookmark bookmark1 = createTestBookmark("Recent 1");
    bookmark1.lastAccessed = now.addSecs(-10);

    EnhancedBookmark bookmark2 = createTestBookmark("Recent 2");
    bookmark2.lastAccessed = now.addSecs(-5);

    EnhancedBookmark bookmark3 = createTestBookmark("Old");
    bookmark3.lastAccessed = now.addDays(-1);

    m_bookmarkManager->addBookmark(bookmark1);
    m_bookmarkManager->addBookmark(bookmark2);
    m_bookmarkManager->addBookmark(bookmark3);

    QList<EnhancedBookmark> recent = m_bookmarkManager->getRecentBookmarks(2);
    QCOMPARE(recent.size(), 2);

    // Should be sorted by most recent first
    QCOMPARE(recent[0].name, QString("Recent 2"));
    QCOMPARE(recent[1].name, QString("Recent 1"));
}

void TestBookmarkManager::testGetMostAccessedBookmarks()
{
    // Add bookmarks with different access counts
    EnhancedBookmark bookmark1 = createTestBookmark("Low Access");
    bookmark1.accessCount = 1;

    EnhancedBookmark bookmark2 = createTestBookmark("High Access");
    bookmark2.accessCount = 10;

    EnhancedBookmark bookmark3 = createTestBookmark("Medium Access");
    bookmark3.accessCount = 5;

    m_bookmarkManager->addBookmark(bookmark1);
    m_bookmarkManager->addBookmark(bookmark2);
    m_bookmarkManager->addBookmark(bookmark3);

    QList<EnhancedBookmark> mostAccessed = m_bookmarkManager->getMostAccessedBookmarks(2);
    QCOMPARE(mostAccessed.size(), 2);

    // Should be sorted by highest access count first
    QCOMPARE(mostAccessed[0].name, QString("High Access"));
    QCOMPARE(mostAccessed[1].name, QString("Medium Access"));
}

void TestBookmarkManager::testEnhancedBookmarkStructure()
{
    EnhancedBookmark bookmark;

    // Test default values
    QCOMPARE(bookmark.pageNumber, 0);
    QCOMPARE(bookmark.zoomFactor, 1.0);
    QCOMPARE(bookmark.rotation, 0);
    QCOMPARE(bookmark.accessCount, 0);
    QCOMPARE(bookmark.color, Qt::blue);
    QCOMPARE(bookmark.isFavorite, false);
    QVERIFY(bookmark.created.isValid());
    QVERIFY(bookmark.lastAccessed.isValid());

    // Test setting values
    bookmark.name = "Test Name";
    bookmark.description = "Test Description";
    bookmark.filePath = "/test/path.pdf";
    bookmark.pageNumber = 5;
    bookmark.zoomFactor = 1.5;
    bookmark.rotation = 90;
    bookmark.tags = QStringList() << "tag1" << "tag2";
    bookmark.category = "Test Category";
    bookmark.notes = "Test Notes";
    bookmark.isFavorite = true;

    QCOMPARE(bookmark.name, QString("Test Name"));
    QCOMPARE(bookmark.description, QString("Test Description"));
    QCOMPARE(bookmark.filePath, QString("/test/path.pdf"));
    QCOMPARE(bookmark.pageNumber, 5);
    QCOMPARE(bookmark.zoomFactor, 1.5);
    QCOMPARE(bookmark.rotation, 90);
    QCOMPARE(bookmark.tags.size(), 2);
    QCOMPARE(bookmark.category, QString("Test Category"));
    QCOMPARE(bookmark.notes, QString("Test Notes"));
    QCOMPARE(bookmark.isFavorite, true);
}

void TestBookmarkManager::testBookmarkSerialization()
{
    EnhancedBookmark bookmark = createTestBookmark();
    bookmark.id = "test-id-123";
    bookmark.viewportRect = QRectF(10, 20, 100, 200);

    QJsonObject json = bookmark.toJson();

    QVERIFY(json.contains("id"));
    QVERIFY(json.contains("name"));
    QVERIFY(json.contains("filePath"));
    QVERIFY(json.contains("pageNumber"));
    QVERIFY(json.contains("zoomFactor"));
    QVERIFY(json.contains("tags"));
    QVERIFY(json.contains("category"));
    QVERIFY(json.contains("isFavorite"));

    QCOMPARE(json["id"].toString(), QString("test-id-123"));
    QCOMPARE(json["name"].toString(), bookmark.name);
    QCOMPARE(json["filePath"].toString(), bookmark.filePath);
    QCOMPARE(json["pageNumber"].toInt(), bookmark.pageNumber);
    QCOMPARE(json["zoomFactor"].toDouble(), bookmark.zoomFactor);
    QCOMPARE(json["isFavorite"].toBool(), bookmark.isFavorite);
}

void TestBookmarkManager::testBookmarkDeserialization()
{
    // Create JSON object
    QJsonObject json;
    json["id"] = "test-id-456";
    json["name"] = "Deserialized Bookmark";
    json["description"] = "Test Description";
    json["filePath"] = "/test/deserialize.pdf";
    json["fileName"] = "deserialize.pdf";
    json["pageNumber"] = 3;
    json["zoomFactor"] = 1.25;
    json["rotation"] = 180;
    json["accessCount"] = 7;
    json["isFavorite"] = true;
    json["category"] = "Deserialized";
    json["notes"] = "Deserialized notes";

    QJsonArray tagsArray;
    tagsArray.append("tag1");
    tagsArray.append("tag2");
    json["tags"] = tagsArray;

    // Deserialize
    EnhancedBookmark bookmark = EnhancedBookmark::fromJson(json);

    QCOMPARE(bookmark.id, QString("test-id-456"));
    QCOMPARE(bookmark.name, QString("Deserialized Bookmark"));
    QCOMPARE(bookmark.description, QString("Test Description"));
    QCOMPARE(bookmark.filePath, QString("/test/deserialize.pdf"));
    QCOMPARE(bookmark.fileName, QString("deserialize.pdf"));
    QCOMPARE(bookmark.pageNumber, 3);
    QCOMPARE(bookmark.zoomFactor, 1.25);
    QCOMPARE(bookmark.rotation, 180);
    QCOMPARE(bookmark.accessCount, 7);
    QCOMPARE(bookmark.isFavorite, true);
    QCOMPARE(bookmark.category, QString("Deserialized"));
    QCOMPARE(bookmark.notes, QString("Deserialized notes"));
    QCOMPARE(bookmark.tags.size(), 2);
    QVERIFY(bookmark.tags.contains("tag1"));
    QVERIFY(bookmark.tags.contains("tag2"));
}

void TestBookmarkManager::testBookmarkComparison()
{
    EnhancedBookmark bookmark1 = createTestBookmark();
    bookmark1.id = "same-id";

    EnhancedBookmark bookmark2 = createTestBookmark();
    bookmark2.id = "same-id";
    bookmark2.name = "Different Name"; // Different content but same ID

    EnhancedBookmark bookmark3 = createTestBookmark();
    bookmark3.id = "different-id";

    // Comparison is based on ID only
    QVERIFY(bookmark1 == bookmark2);
    QVERIFY(!(bookmark1 == bookmark3));
}

void TestBookmarkManager::testBookmarkIdGeneration()
{
    EnhancedBookmark bookmark1;
    EnhancedBookmark bookmark2;

    bookmark1.generateId();
    bookmark2.generateId();

    QVERIFY(!bookmark1.id.isEmpty());
    QVERIFY(!bookmark2.id.isEmpty());
    QVERIFY(bookmark1.id != bookmark2.id); // Should be unique
}

void TestBookmarkManager::testBookmarkAccessUpdate()
{
    EnhancedBookmark bookmark = createTestBookmark();
    QDateTime originalAccess = bookmark.lastAccessed;
    int originalCount = bookmark.accessCount;

    // Wait a bit to ensure timestamp difference
    QTest::qWait(10);

    bookmark.updateAccess();

    QVERIFY(bookmark.lastAccessed > originalAccess);
    QCOMPARE(bookmark.accessCount, originalCount + 1);
}

void TestBookmarkManager::testSearchBookmarks()
{
    // Add bookmarks with different names and descriptions
    EnhancedBookmark bookmark1 = createTestBookmark("PDF Document Analysis");
    bookmark1.description = "Analysis of financial reports";

    EnhancedBookmark bookmark2 = createTestBookmark("Research Paper");
    bookmark2.description = "Machine learning research";

    EnhancedBookmark bookmark3 = createTestBookmark("User Manual");
    bookmark3.description = "Software documentation";

    m_bookmarkManager->addBookmark(bookmark1);
    m_bookmarkManager->addBookmark(bookmark2);
    m_bookmarkManager->addBookmark(bookmark3);

    // Search by name
    QList<EnhancedBookmark> results1 = m_bookmarkManager->searchBookmarks("PDF");
    QCOMPARE(results1.size(), 1);
    QCOMPARE(results1.first().name, QString("PDF Document Analysis"));

    // Search by description
    QList<EnhancedBookmark> results2 = m_bookmarkManager->searchBookmarks("research");
    QCOMPARE(results2.size(), 1);
    QCOMPARE(results2.first().name, QString("Research Paper"));

    // Search with no results
    QList<EnhancedBookmark> results3 = m_bookmarkManager->searchBookmarks("nonexistent");
    QVERIFY(results3.isEmpty());
}

void TestBookmarkManager::testFilterBookmarks()
{
    // Add bookmarks with different properties
    EnhancedBookmark bookmark1 = createTestBookmark("Favorite Doc");
    bookmark1.isFavorite = true;
    bookmark1.category = "Important";
    bookmark1.tags = QStringList() << "urgent" << "review";

    EnhancedBookmark bookmark2 = createTestBookmark("Regular Doc");
    bookmark2.isFavorite = false;
    bookmark2.category = "General";
    bookmark2.tags = QStringList() << "reference";

    m_bookmarkManager->addBookmark(bookmark1);
    m_bookmarkManager->addBookmark(bookmark2);

    // Filter by favorites
    QList<EnhancedBookmark> favorites = m_bookmarkManager->filterBookmarks(BookmarkManager::FilterType::Favorites);
    QCOMPARE(favorites.size(), 1);
    QCOMPARE(favorites.first().name, QString("Favorite Doc"));

    // Filter by category
    QList<EnhancedBookmark> important = m_bookmarkManager->filterBookmarks(BookmarkManager::FilterType::ByCategory, "Important");
    QCOMPARE(important.size(), 1);
    QCOMPARE(important.first().name, QString("Favorite Doc"));

    // Filter by tag
    QList<EnhancedBookmark> urgent = m_bookmarkManager->filterBookmarks(BookmarkManager::FilterType::ByTag, "urgent");
    QCOMPARE(urgent.size(), 1);
    QCOMPARE(urgent.first().name, QString("Favorite Doc"));
}

void TestBookmarkManager::testSortBookmarks()
{
    // Add bookmarks with different properties for sorting
    QDateTime now = QDateTime::currentDateTime();

    EnhancedBookmark bookmark1 = createTestBookmark("Z Bookmark");
    bookmark1.created = now.addDays(-2);
    bookmark1.lastAccessed = now.addHours(-1);
    bookmark1.accessCount = 5;
    bookmark1.pageNumber = 10;

    EnhancedBookmark bookmark2 = createTestBookmark("A Bookmark");
    bookmark2.created = now.addDays(-1);
    bookmark2.lastAccessed = now.addHours(-2);
    bookmark2.accessCount = 10;
    bookmark2.pageNumber = 5;

    m_bookmarkManager->addBookmark(bookmark1);
    m_bookmarkManager->addBookmark(bookmark2);

    // Sort by name
    QList<EnhancedBookmark> sortedByName = m_bookmarkManager->sortBookmarks(BookmarkManager::SortOrder::Name);
    QCOMPARE(sortedByName.size(), 2);
    QCOMPARE(sortedByName[0].name, QString("A Bookmark"));
    QCOMPARE(sortedByName[1].name, QString("Z Bookmark"));

    // Sort by access count
    QList<EnhancedBookmark> sortedByAccess = m_bookmarkManager->sortBookmarks(BookmarkManager::SortOrder::AccessCount);
    QCOMPARE(sortedByAccess.size(), 2);
    QCOMPARE(sortedByAccess[0].accessCount, 10); // Higher count first
    QCOMPARE(sortedByAccess[1].accessCount, 5);
}

void TestBookmarkManager::testGetBookmarksByCategory()
{
    // Add bookmarks with different categories
    EnhancedBookmark bookmark1 = createTestBookmark("Work Doc");
    bookmark1.category = "Work";

    EnhancedBookmark bookmark2 = createTestBookmark("Personal Doc");
    bookmark2.category = "Personal";

    EnhancedBookmark bookmark3 = createTestBookmark("Another Work Doc");
    bookmark3.category = "Work";

    m_bookmarkManager->addBookmark(bookmark1);
    m_bookmarkManager->addBookmark(bookmark2);
    m_bookmarkManager->addBookmark(bookmark3);

    QList<EnhancedBookmark> workBookmarks = m_bookmarkManager->getBookmarksByCategory("Work");
    QCOMPARE(workBookmarks.size(), 2);

    for (const EnhancedBookmark& bookmark : workBookmarks) {
        QCOMPARE(bookmark.category, QString("Work"));
    }

    QList<EnhancedBookmark> personalBookmarks = m_bookmarkManager->getBookmarksByCategory("Personal");
    QCOMPARE(personalBookmarks.size(), 1);
    QCOMPARE(personalBookmarks.first().category, QString("Personal"));
}

void TestBookmarkManager::testGetBookmarksByTag()
{
    // Add bookmarks with different tags
    EnhancedBookmark bookmark1 = createTestBookmark("Tagged Doc 1");
    bookmark1.tags = QStringList() << "important" << "review";

    EnhancedBookmark bookmark2 = createTestBookmark("Tagged Doc 2");
    bookmark2.tags = QStringList() << "reference" << "important";

    EnhancedBookmark bookmark3 = createTestBookmark("Untagged Doc");
    bookmark3.tags = QStringList() << "other";

    m_bookmarkManager->addBookmark(bookmark1);
    m_bookmarkManager->addBookmark(bookmark2);
    m_bookmarkManager->addBookmark(bookmark3);

    QList<EnhancedBookmark> importantBookmarks = m_bookmarkManager->getBookmarksByTag("important");
    QCOMPARE(importantBookmarks.size(), 2);

    for (const EnhancedBookmark& bookmark : importantBookmarks) {
        QVERIFY(bookmark.tags.contains("important"));
    }

    QList<EnhancedBookmark> reviewBookmarks = m_bookmarkManager->getBookmarksByTag("review");
    QCOMPARE(reviewBookmarks.size(), 1);
    QCOMPARE(reviewBookmarks.first().name, QString("Tagged Doc 1"));
}

void TestBookmarkManager::testGetAllCategories()
{
    // Add bookmarks with different categories
    EnhancedBookmark bookmark1 = createTestBookmark("Doc 1");
    bookmark1.category = "Work";

    EnhancedBookmark bookmark2 = createTestBookmark("Doc 2");
    bookmark2.category = "Personal";

    EnhancedBookmark bookmark3 = createTestBookmark("Doc 3");
    bookmark3.category = "Work"; // Duplicate category

    m_bookmarkManager->addBookmark(bookmark1);
    m_bookmarkManager->addBookmark(bookmark2);
    m_bookmarkManager->addBookmark(bookmark3);

    QStringList categories = m_bookmarkManager->getAllCategories();
    QCOMPARE(categories.size(), 2); // Should be unique
    QVERIFY(categories.contains("Work"));
    QVERIFY(categories.contains("Personal"));
}

void TestBookmarkManager::testGetAllTags()
{
    // Add bookmarks with different tags
    EnhancedBookmark bookmark1 = createTestBookmark("Doc 1");
    bookmark1.tags = QStringList() << "tag1" << "tag2";

    EnhancedBookmark bookmark2 = createTestBookmark("Doc 2");
    bookmark2.tags = QStringList() << "tag2" << "tag3"; // tag2 is duplicate

    m_bookmarkManager->addBookmark(bookmark1);
    m_bookmarkManager->addBookmark(bookmark2);

    QStringList tags = m_bookmarkManager->getAllTags();
    QCOMPARE(tags.size(), 3); // Should be unique
    QVERIFY(tags.contains("tag1"));
    QVERIFY(tags.contains("tag2"));
    QVERIFY(tags.contains("tag3"));
}

void TestBookmarkManager::testGetCategoriesForDocument()
{
    QString docPath = "/test/document.pdf";

    // Add bookmarks for the same document with different categories
    EnhancedBookmark bookmark1 = createTestBookmark("Bookmark 1", docPath);
    bookmark1.category = "Important";

    EnhancedBookmark bookmark2 = createTestBookmark("Bookmark 2", docPath);
    bookmark2.category = "Reference";

    EnhancedBookmark bookmark3 = createTestBookmark("Bookmark 3", "/other/doc.pdf");
    bookmark3.category = "Other"; // Different document

    m_bookmarkManager->addBookmark(bookmark1);
    m_bookmarkManager->addBookmark(bookmark2);
    m_bookmarkManager->addBookmark(bookmark3);

    QStringList categories = m_bookmarkManager->getCategoriesForDocument(docPath);
    QCOMPARE(categories.size(), 2);
    QVERIFY(categories.contains("Important"));
    QVERIFY(categories.contains("Reference"));
    QVERIFY(!categories.contains("Other"));
}

void TestBookmarkManager::testGetTagsForDocument()
{
    QString docPath = "/test/document.pdf";

    // Add bookmarks for the same document with different tags
    EnhancedBookmark bookmark1 = createTestBookmark("Bookmark 1", docPath);
    bookmark1.tags = QStringList() << "tag1" << "tag2";

    EnhancedBookmark bookmark2 = createTestBookmark("Bookmark 2", docPath);
    bookmark2.tags = QStringList() << "tag2" << "tag3"; // tag2 is duplicate

    EnhancedBookmark bookmark3 = createTestBookmark("Bookmark 3", "/other/doc.pdf");
    bookmark3.tags = QStringList() << "tag4"; // Different document

    m_bookmarkManager->addBookmark(bookmark1);
    m_bookmarkManager->addBookmark(bookmark2);
    m_bookmarkManager->addBookmark(bookmark3);

    QStringList tags = m_bookmarkManager->getTagsForDocument(docPath);
    QCOMPARE(tags.size(), 3); // Should be unique
    QVERIFY(tags.contains("tag1"));
    QVERIFY(tags.contains("tag2"));
    QVERIFY(tags.contains("tag3"));
    QVERIFY(!tags.contains("tag4"));
}

void TestBookmarkManager::testMostUsedCategories()
{
    // Add bookmarks with different categories and frequencies
    for (int i = 0; i < 5; ++i) {
        EnhancedBookmark bookmark = createTestBookmark(QString("Work %1").arg(i));
        bookmark.category = "Work";
        m_bookmarkManager->addBookmark(bookmark);
    }

    for (int i = 0; i < 3; ++i) {
        EnhancedBookmark bookmark = createTestBookmark(QString("Personal %1").arg(i));
        bookmark.category = "Personal";
        m_bookmarkManager->addBookmark(bookmark);
    }

    for (int i = 0; i < 1; ++i) {
        EnhancedBookmark bookmark = createTestBookmark(QString("Other %1").arg(i));
        bookmark.category = "Other";
        m_bookmarkManager->addBookmark(bookmark);
    }

    QStringList mostUsed = m_bookmarkManager->getMostUsedCategories(2);
    QCOMPARE(mostUsed.size(), 2);
    QCOMPARE(mostUsed[0], QString("Work")); // Most frequent
    QCOMPARE(mostUsed[1], QString("Personal")); // Second most frequent
}

void TestBookmarkManager::testMostUsedTags()
{
    // Add bookmarks with different tags and frequencies
    for (int i = 0; i < 3; ++i) {
        EnhancedBookmark bookmark = createTestBookmark(QString("Doc %1").arg(i));
        bookmark.tags = QStringList() << "frequent" << "common";
        m_bookmarkManager->addBookmark(bookmark);
    }

    for (int i = 0; i < 2; ++i) {
        EnhancedBookmark bookmark = createTestBookmark(QString("Doc2 %1").arg(i));
        bookmark.tags = QStringList() << "common" << "rare";
        m_bookmarkManager->addBookmark(bookmark);
    }

    QStringList mostUsed = m_bookmarkManager->getMostUsedTags(3);
    QVERIFY(mostUsed.size() <= 3);
    QVERIFY(mostUsed.contains("common")); // Should be most frequent
    QVERIFY(mostUsed.contains("frequent"));
}

void TestBookmarkManager::testGetBookmarkCount()
{
    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 0);

    // Add bookmarks
    for (int i = 0; i < 5; ++i) {
        EnhancedBookmark bookmark = createTestBookmark(QString("Bookmark %1").arg(i));
        m_bookmarkManager->addBookmark(bookmark);
    }

    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 5);

    // Remove one bookmark
    QList<EnhancedBookmark> all = m_bookmarkManager->getAllBookmarks();
    m_bookmarkManager->removeBookmark(all.first().id);

    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 4);
}

void TestBookmarkManager::testGetBookmarkCountForDocument()
{
    QString doc1 = "/test/doc1.pdf";
    QString doc2 = "/test/doc2.pdf";

    // Add bookmarks for different documents
    for (int i = 0; i < 3; ++i) {
        EnhancedBookmark bookmark = createTestBookmark(QString("Doc1 Bookmark %1").arg(i), doc1);
        m_bookmarkManager->addBookmark(bookmark);
    }

    for (int i = 0; i < 2; ++i) {
        EnhancedBookmark bookmark = createTestBookmark(QString("Doc2 Bookmark %1").arg(i), doc2);
        m_bookmarkManager->addBookmark(bookmark);
    }

    QCOMPARE(m_bookmarkManager->getBookmarkCountForDocument(doc1), 3);
    QCOMPARE(m_bookmarkManager->getBookmarkCountForDocument(doc2), 2);
    QCOMPARE(m_bookmarkManager->getBookmarkCountForDocument("/nonexistent.pdf"), 0);
}

void TestBookmarkManager::testGetLastBookmarkTime()
{
    QDateTime before = QDateTime::currentDateTime();

    // Add a bookmark
    EnhancedBookmark bookmark = createTestBookmark();
    m_bookmarkManager->addBookmark(bookmark);

    QDateTime after = QDateTime::currentDateTime();
    QDateTime lastTime = m_bookmarkManager->getLastBookmarkTime();

    QVERIFY(lastTime >= before);
    QVERIFY(lastTime <= after);
}

void TestBookmarkManager::testToggleFavorite()
{
    QSignalSpy spy(m_bookmarkManager, &BookmarkManager::favoriteToggled);

    // Add a bookmark
    EnhancedBookmark bookmark = createTestBookmark();
    bookmark.isFavorite = false;
    QString id = m_bookmarkManager->addBookmark(bookmark);

    // Toggle to favorite
    bool result = m_bookmarkManager->toggleFavorite(id);
    QVERIFY(result);
    QCOMPARE(spy.count(), 1);

    QList<QVariant> args = spy.takeFirst();
    QCOMPARE(args.at(0).toString(), id);
    QCOMPARE(args.at(1).toBool(), true);

    // Verify bookmark is now favorite
    EnhancedBookmark updated = m_bookmarkManager->getBookmark(id);
    QVERIFY(updated.isFavorite);

    // Toggle back to non-favorite
    result = m_bookmarkManager->toggleFavorite(id);
    QVERIFY(result);
    QCOMPARE(spy.count(), 1);

    args = spy.takeFirst();
    QCOMPARE(args.at(1).toBool(), false);

    updated = m_bookmarkManager->getBookmark(id);
    QVERIFY(!updated.isFavorite);
}

void TestBookmarkManager::testSetFavorite()
{
    QSignalSpy spy(m_bookmarkManager, &BookmarkManager::favoriteToggled);

    // Add a bookmark
    EnhancedBookmark bookmark = createTestBookmark();
    bookmark.isFavorite = false;
    QString id = m_bookmarkManager->addBookmark(bookmark);

    // Set as favorite
    bool result = m_bookmarkManager->setFavorite(id, true);
    QVERIFY(result);
    QCOMPARE(spy.count(), 1);

    EnhancedBookmark updated = m_bookmarkManager->getBookmark(id);
    QVERIFY(updated.isFavorite);

    // Set as non-favorite
    result = m_bookmarkManager->setFavorite(id, false);
    QVERIFY(result);
    QCOMPARE(spy.count(), 2);

    updated = m_bookmarkManager->getBookmark(id);
    QVERIFY(!updated.isFavorite);

    // Test with non-existent bookmark
    result = m_bookmarkManager->setFavorite("non-existent", true);
    QVERIFY(!result);
}

void TestBookmarkManager::testRecordAccess()
{
    QSignalSpy spy(m_bookmarkManager, &BookmarkManager::bookmarkAccessed);

    // Add a bookmark
    EnhancedBookmark bookmark = createTestBookmark();
    bookmark.accessCount = 0;
    QString id = m_bookmarkManager->addBookmark(bookmark);

    QDateTime beforeAccess = QDateTime::currentDateTime();

    // Record access
    m_bookmarkManager->recordAccess(id);

    QCOMPARE(spy.count(), 1);
    QList<QVariant> args = spy.takeFirst();
    QCOMPARE(args.at(0).toString(), id);

    // Verify access was recorded
    EnhancedBookmark updated = m_bookmarkManager->getBookmark(id);
    QCOMPARE(updated.accessCount, 1);
    QVERIFY(updated.lastAccessed >= beforeAccess);
}

void TestBookmarkManager::testAccessCountTracking()
{
    // Add a bookmark
    EnhancedBookmark bookmark = createTestBookmark();
    bookmark.accessCount = 0;
    QString id = m_bookmarkManager->addBookmark(bookmark);

    // Record multiple accesses
    for (int i = 0; i < 5; ++i) {
        m_bookmarkManager->recordAccess(id);
    }

    EnhancedBookmark updated = m_bookmarkManager->getBookmark(id);
    QCOMPARE(updated.accessCount, 5);
}

void TestBookmarkManager::testLastAccessedTracking()
{
    // Add a bookmark
    EnhancedBookmark bookmark = createTestBookmark();
    QString id = m_bookmarkManager->addBookmark(bookmark);

    QDateTime firstAccess = m_bookmarkManager->getBookmark(id).lastAccessed;

    // Wait and record another access
    QTest::qWait(10);
    m_bookmarkManager->recordAccess(id);

    QDateTime secondAccess = m_bookmarkManager->getBookmark(id).lastAccessed;
    QVERIFY(secondAccess > firstAccess);
}

void TestBookmarkManager::testExportBookmarks()
{
    // Add some bookmarks
    for (int i = 0; i < 3; ++i) {
        EnhancedBookmark bookmark = createTestBookmark(QString("Export Bookmark %1").arg(i));
        m_bookmarkManager->addBookmark(bookmark);
    }

    QString exportPath = m_tempDir->path() + "/exported_bookmarks.json";

    // Export bookmarks
    bool result = m_bookmarkManager->exportBookmarks(exportPath);
    QVERIFY(result);

    // Verify file was created
    QFile exportFile(exportPath);
    QVERIFY(exportFile.exists());
    QVERIFY(exportFile.size() > 0);

    // Verify JSON content
    QVERIFY(exportFile.open(QIODevice::ReadOnly));
    QJsonDocument doc = QJsonDocument::fromJson(exportFile.readAll());
    exportFile.close();

    QVERIFY(doc.isArray());
    QJsonArray array = doc.array();
    QCOMPARE(array.size(), 3);
}

void TestBookmarkManager::testImportBookmarks()
{
    // Create JSON file with bookmarks
    QJsonArray bookmarksArray;

    for (int i = 0; i < 2; ++i) {
        EnhancedBookmark bookmark = createTestBookmark(QString("Import Bookmark %1").arg(i));
        bookmarksArray.append(bookmark.toJson());
    }

    QJsonDocument doc(bookmarksArray);
    QString importPath = m_tempDir->path() + "/import_bookmarks.json";

    QFile importFile(importPath);
    QVERIFY(importFile.open(QIODevice::WriteOnly));
    importFile.write(doc.toJson());
    importFile.close();

    // Clear existing bookmarks
    m_bookmarkManager->clearAllBookmarks();
    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 0);

    // Import bookmarks
    bool result = m_bookmarkManager->importBookmarks(importPath);
    QVERIFY(result);

    // Verify bookmarks were imported
    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 2);

    QList<EnhancedBookmark> imported = m_bookmarkManager->getAllBookmarks();
    QVERIFY(containsBookmarkWithName(imported, "Import Bookmark 0"));
    QVERIFY(containsBookmarkWithName(imported, "Import Bookmark 1"));
}

void TestBookmarkManager::testExportBookmarksForDocument()
{
    QString doc1 = "/test/doc1.pdf";
    QString doc2 = "/test/doc2.pdf";

    // Add bookmarks for different documents
    EnhancedBookmark bookmark1 = createTestBookmark("Doc1 Bookmark", doc1);
    EnhancedBookmark bookmark2 = createTestBookmark("Doc2 Bookmark", doc2);

    m_bookmarkManager->addBookmark(bookmark1);
    m_bookmarkManager->addBookmark(bookmark2);

    QString exportPath = m_tempDir->path() + "/doc1_bookmarks.json";

    // Export bookmarks for doc1 only
    bool result = m_bookmarkManager->exportBookmarksForDocument(doc1, exportPath);
    QVERIFY(result);

    // Verify file content
    QFile exportFile(exportPath);
    QVERIFY(exportFile.open(QIODevice::ReadOnly));
    QJsonDocument doc = QJsonDocument::fromJson(exportFile.readAll());
    exportFile.close();

    QVERIFY(doc.isArray());
    QJsonArray array = doc.array();
    QCOMPARE(array.size(), 1); // Only doc1 bookmark should be exported

    QJsonObject bookmarkObj = array[0].toObject();
    QCOMPARE(bookmarkObj["filePath"].toString(), doc1);
}

void TestBookmarkManager::testSaveToSettings()
{
    // Add some bookmarks
    for (int i = 0; i < 3; ++i) {
        EnhancedBookmark bookmark = createTestBookmark(QString("Settings Bookmark %1").arg(i));
        m_bookmarkManager->addBookmark(bookmark);
    }

    // Save to settings
    m_bookmarkManager->saveToSettings();

    // Create new manager and verify bookmarks are loaded
    BookmarkManager* newManager = new BookmarkManager(this);
    QCOMPARE(newManager->getBookmarkCount(), 3);

    QList<EnhancedBookmark> loaded = newManager->getAllBookmarks();
    QVERIFY(containsBookmarkWithName(loaded, "Settings Bookmark 0"));
    QVERIFY(containsBookmarkWithName(loaded, "Settings Bookmark 1"));
    QVERIFY(containsBookmarkWithName(loaded, "Settings Bookmark 2"));

    delete newManager;
}

void TestBookmarkManager::testLoadFromSettings()
{
    // Add bookmarks and save
    EnhancedBookmark bookmark1 = createTestBookmark("Load Test 1");
    EnhancedBookmark bookmark2 = createTestBookmark("Load Test 2");

    m_bookmarkManager->addBookmark(bookmark1);
    m_bookmarkManager->addBookmark(bookmark2);
    m_bookmarkManager->saveToSettings();

    // Clear current bookmarks
    m_bookmarkManager->clearAllBookmarks();
    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 0);

    // Load from settings
    m_bookmarkManager->loadFromSettings();

    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 2);
    QList<EnhancedBookmark> loaded = m_bookmarkManager->getAllBookmarks();
    QVERIFY(containsBookmarkWithName(loaded, "Load Test 1"));
    QVERIFY(containsBookmarkWithName(loaded, "Load Test 2"));
}

void TestBookmarkManager::testAutoSaveEnabled()
{
    m_bookmarkManager->setAutoSaveEnabled(true);
    QVERIFY(m_bookmarkManager->isAutoSaveEnabled());

    // Add a bookmark - should auto-save
    EnhancedBookmark bookmark = createTestBookmark("Auto Save Test");
    m_bookmarkManager->addBookmark(bookmark);

    // Create new manager - should load the auto-saved bookmark
    BookmarkManager* newManager = new BookmarkManager(this);
    QCOMPARE(newManager->getBookmarkCount(), 1);

    QList<EnhancedBookmark> loaded = newManager->getAllBookmarks();
    QVERIFY(containsBookmarkWithName(loaded, "Auto Save Test"));

    delete newManager;
}

void TestBookmarkManager::testAutoSaveDisabled()
{
    m_bookmarkManager->setAutoSaveEnabled(false);
    QVERIFY(!m_bookmarkManager->isAutoSaveEnabled());

    // Add a bookmark - should not auto-save
    EnhancedBookmark bookmark = createTestBookmark("No Auto Save Test");
    m_bookmarkManager->addBookmark(bookmark);

    // Create new manager - should not load the bookmark
    BookmarkManager* newManager = new BookmarkManager(this);
    QCOMPARE(newManager->getBookmarkCount(), 0);

    delete newManager;
}

void TestBookmarkManager::testIsValidBookmark()
{
    // Valid bookmark
    EnhancedBookmark validBookmark = createTestBookmark();
    QVERIFY(m_bookmarkManager->isValidBookmark(validBookmark));

    // Invalid bookmark - empty name
    EnhancedBookmark invalidName = createTestBookmark();
    invalidName.name = "";
    QVERIFY(!m_bookmarkManager->isValidBookmark(invalidName));

    // Invalid bookmark - empty file path
    EnhancedBookmark invalidPath = createTestBookmark();
    invalidPath.filePath = "";
    QVERIFY(!m_bookmarkManager->isValidBookmark(invalidPath));

    // Invalid bookmark - negative page number
    EnhancedBookmark invalidPage = createTestBookmark();
    invalidPage.pageNumber = -1;
    QVERIFY(!m_bookmarkManager->isValidBookmark(invalidPage));
}

void TestBookmarkManager::testBookmarkExists()
{
    // Test non-existent bookmark
    QVERIFY(!m_bookmarkManager->bookmarkExists("non-existent-id"));

    // Add a bookmark
    EnhancedBookmark bookmark = createTestBookmark();
    QString id = m_bookmarkManager->addBookmark(bookmark);

    // Test existing bookmark
    QVERIFY(m_bookmarkManager->bookmarkExists(id));

    // Remove bookmark
    m_bookmarkManager->removeBookmark(id);

    // Test removed bookmark
    QVERIFY(!m_bookmarkManager->bookmarkExists(id));
}

void TestBookmarkManager::testHasBookmarkForPage()
{
    QString filePath = "/test/document.pdf";

    // Test non-existent page
    QVERIFY(!m_bookmarkManager->hasBookmarkForPage(filePath, 5));

    // Add bookmark for page 5
    EnhancedBookmark bookmark = createTestBookmark("Page 5 Bookmark", filePath, 5);
    m_bookmarkManager->addBookmark(bookmark);

    // Test existing page
    QVERIFY(m_bookmarkManager->hasBookmarkForPage(filePath, 5));

    // Test different page
    QVERIFY(!m_bookmarkManager->hasBookmarkForPage(filePath, 10));

    // Test different document
    QVERIFY(!m_bookmarkManager->hasBookmarkForPage("/other/doc.pdf", 5));
}

void TestBookmarkManager::testRemoveOrphanedBookmarks()
{
    // This test would require file system interaction to check if files exist
    // For now, we'll test that the method doesn't crash

    // Add some bookmarks
    EnhancedBookmark bookmark1 = createTestBookmark("Existing", "/existing/file.pdf");
    EnhancedBookmark bookmark2 = createTestBookmark("Orphaned", "/nonexistent/file.pdf");

    m_bookmarkManager->addBookmark(bookmark1);
    m_bookmarkManager->addBookmark(bookmark2);

    // Call remove orphaned bookmarks
    m_bookmarkManager->removeOrphanedBookmarks();

    // Should not crash
    QVERIFY(true);
}

void TestBookmarkManager::testRemoveOldBookmarks()
{
    QDateTime now = QDateTime::currentDateTime();

    // Add old bookmark
    EnhancedBookmark oldBookmark = createTestBookmark("Old Bookmark");
    oldBookmark.created = now.addDays(-400); // Older than 365 days
    m_bookmarkManager->addBookmark(oldBookmark);

    // Add recent bookmark
    EnhancedBookmark recentBookmark = createTestBookmark("Recent Bookmark");
    recentBookmark.created = now.addDays(-10); // Recent
    m_bookmarkManager->addBookmark(recentBookmark);

    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 2);

    // Remove bookmarks older than 365 days
    m_bookmarkManager->removeOldBookmarks(365);

    // Should have removed the old bookmark
    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 1);

    QList<EnhancedBookmark> remaining = m_bookmarkManager->getAllBookmarks();
    QCOMPARE(remaining.first().name, QString("Recent Bookmark"));
}

void TestBookmarkManager::testMaxBookmarksConfiguration()
{
    // Set max bookmarks
    m_bookmarkManager->setMaxBookmarks(3);
    QCOMPARE(m_bookmarkManager->getMaxBookmarks(), 3);

    // Test different value
    m_bookmarkManager->setMaxBookmarks(100);
    QCOMPARE(m_bookmarkManager->getMaxBookmarks(), 100);
}

void TestBookmarkManager::testBookmarkTrimming()
{
    // Set small max bookmarks
    m_bookmarkManager->setMaxBookmarks(3);

    // Add more bookmarks than the limit
    for (int i = 0; i < 5; ++i) {
        EnhancedBookmark bookmark = createTestBookmark(QString("Trim Test %1").arg(i));
        m_bookmarkManager->addBookmark(bookmark);
    }

    // Should be trimmed to max bookmarks
    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 3);

    // Should keep the most recent bookmarks
    QList<EnhancedBookmark> remaining = m_bookmarkManager->getAllBookmarks();
    QVERIFY(containsBookmarkWithName(remaining, "Trim Test 2"));
    QVERIFY(containsBookmarkWithName(remaining, "Trim Test 3"));
    QVERIFY(containsBookmarkWithName(remaining, "Trim Test 4"));
}

void TestBookmarkManager::testBookmarkSignals()
{
    QSignalSpy addSpy(m_bookmarkManager, &BookmarkManager::bookmarkAdded);
    QSignalSpy updateSpy(m_bookmarkManager, &BookmarkManager::bookmarkUpdated);
    QSignalSpy removeSpy(m_bookmarkManager, &BookmarkManager::bookmarkRemoved);
    QSignalSpy clearSpy(m_bookmarkManager, &BookmarkManager::bookmarksCleared);

    // Test add signal
    EnhancedBookmark bookmark = createTestBookmark();
    QString id = m_bookmarkManager->addBookmark(bookmark);
    QCOMPARE(addSpy.count(), 1);

    // Test update signal
    bookmark.name = "Updated Name";
    m_bookmarkManager->updateBookmark(id, bookmark);
    QCOMPARE(updateSpy.count(), 1);

    // Test remove signal
    m_bookmarkManager->removeBookmark(id);
    QCOMPARE(removeSpy.count(), 1);

    // Test clear signal
    m_bookmarkManager->addBookmark(createTestBookmark());
    m_bookmarkManager->clearAllBookmarks();
    QCOMPARE(clearSpy.count(), 1);
}

void TestBookmarkManager::testFavoriteSignals()
{
    QSignalSpy favoriteSpy(m_bookmarkManager, &BookmarkManager::favoriteToggled);

    // Add a bookmark
    EnhancedBookmark bookmark = createTestBookmark();
    bookmark.isFavorite = false;
    QString id = m_bookmarkManager->addBookmark(bookmark);

    // Toggle favorite
    m_bookmarkManager->toggleFavorite(id);
    QCOMPARE(favoriteSpy.count(), 1);

    QList<QVariant> args = favoriteSpy.takeFirst();
    QCOMPARE(args.at(0).toString(), id);
    QCOMPARE(args.at(1).toBool(), true);

    // Set favorite explicitly
    m_bookmarkManager->setFavorite(id, false);
    QCOMPARE(favoriteSpy.count(), 1);

    args = favoriteSpy.takeFirst();
    QCOMPARE(args.at(1).toBool(), false);
}

void TestBookmarkManager::testAccessSignals()
{
    QSignalSpy accessSpy(m_bookmarkManager, &BookmarkManager::bookmarkAccessed);

    // Add a bookmark
    EnhancedBookmark bookmark = createTestBookmark();
    QString id = m_bookmarkManager->addBookmark(bookmark);

    // Record access
    m_bookmarkManager->recordAccess(id);
    QCOMPARE(accessSpy.count(), 1);

    QList<QVariant> args = accessSpy.takeFirst();
    QCOMPARE(args.at(0).toString(), id);
}

void TestBookmarkManager::testDuplicateBookmarks()
{
    // Add bookmark with specific ID
    EnhancedBookmark bookmark1 = createTestBookmark("Duplicate Test");
    bookmark1.id = "duplicate-id";

    // Try to add another bookmark with same ID
    EnhancedBookmark bookmark2 = createTestBookmark("Another Duplicate");
    bookmark2.id = "duplicate-id";

    QString id1 = m_bookmarkManager->addBookmark(bookmark1);
    QString id2 = m_bookmarkManager->addBookmark(bookmark2);

    // Should generate different IDs
    QVERIFY(!id1.isEmpty());
    QVERIFY(!id2.isEmpty());
    QVERIFY(id1 != id2);

    // Both bookmarks should exist
    QCOMPARE(m_bookmarkManager->getBookmarkCount(), 2);
}

void TestBookmarkManager::testInvalidBookmarkData()
{
    // Test with null/empty data
    EnhancedBookmark emptyBookmark;
    QString id = m_bookmarkManager->addBookmark(emptyBookmark);
    QVERIFY(id.isEmpty()); // Should fail validation

    // Test with extreme values
    EnhancedBookmark extremeBookmark = createTestBookmark();
    extremeBookmark.pageNumber = -999999;
    extremeBookmark.zoomFactor = -1.0;
    extremeBookmark.accessCount = -100;

    id = m_bookmarkManager->addBookmark(extremeBookmark);
    QVERIFY(id.isEmpty()); // Should fail validation

    // Test with very long strings
    EnhancedBookmark longBookmark = createTestBookmark();
    longBookmark.name = QString(10000, 'X');
    longBookmark.description = QString(50000, 'Y');
    longBookmark.notes = QString(100000, 'Z');

    id = m_bookmarkManager->addBookmark(longBookmark);
    // Should handle long strings gracefully (may succeed or fail depending on implementation)
    QVERIFY(true); // Test passes if no crash occurs
}

void TestBookmarkManager::testManyBookmarks()
{
    const int bookmarkCount = 1000;

    // Set high max bookmarks to allow all
    m_bookmarkManager->setMaxBookmarks(bookmarkCount + 100);

    // Add many bookmarks
    for (int i = 0; i < bookmarkCount; ++i) {
        EnhancedBookmark bookmark = createTestBookmark(QString("Stress Test %1").arg(i));
        bookmark.pageNumber = i;
        bookmark.accessCount = i % 10;
        bookmark.category = QString("Category %1").arg(i % 5);
        bookmark.tags = QStringList() << QString("tag%1").arg(i % 3) << QString("tag%1").arg(i % 7);

        QString id = m_bookmarkManager->addBookmark(bookmark);
        QVERIFY(!id.isEmpty());

        // Periodically verify count
        if (i % 100 == 99) {
            QCOMPARE(m_bookmarkManager->getBookmarkCount(), i + 1);
        }
    }

    // Final verification
    QCOMPARE(m_bookmarkManager->getBookmarkCount(), bookmarkCount);

    // Test retrieval operations with many bookmarks
    QList<EnhancedBookmark> all = m_bookmarkManager->getAllBookmarks();
    QCOMPARE(all.size(), bookmarkCount);

    // Test search with many bookmarks
    QList<EnhancedBookmark> searchResults = m_bookmarkManager->searchBookmarks("Stress Test 500");
    QCOMPARE(searchResults.size(), 1);

    // Test category filtering
    QList<EnhancedBookmark> categoryResults = m_bookmarkManager->getBookmarksByCategory("Category 0");
    QVERIFY(categoryResults.size() > 0);

    // If we get here, performance with many bookmarks is acceptable
    QVERIFY(true);
}

void TestBookmarkManager::testConcurrentAccess()
{
    // Add some initial bookmarks
    QStringList bookmarkIds;
    for (int i = 0; i < 10; ++i) {
        EnhancedBookmark bookmark = createTestBookmark(QString("Concurrent Test %1").arg(i));
        QString id = m_bookmarkManager->addBookmark(bookmark);
        bookmarkIds.append(id);
    }

    QList<QThread*> threads;

    // Create threads that perform various operations
    for (int i = 0; i < 5; ++i) {
        QThread* thread = QThread::create([this, &bookmarkIds, i]() {
            // Each thread performs different operations
            switch (i % 4) {
            case 0: // Add bookmarks
                for (int j = 0; j < 10; ++j) {
                    EnhancedBookmark bookmark = createTestBookmark(QString("Thread%1 Bookmark %2").arg(i).arg(j));
                    m_bookmarkManager->addBookmark(bookmark);
                }
                break;

            case 1: // Access bookmarks
                for (const QString& id : bookmarkIds) {
                    m_bookmarkManager->recordAccess(id);
                }
                break;

            case 2: // Toggle favorites
                for (const QString& id : bookmarkIds) {
                    m_bookmarkManager->toggleFavorite(id);
                }
                break;

            case 3: // Search and retrieve
                for (int j = 0; j < 10; ++j) {
                    m_bookmarkManager->searchBookmarks("Test");
                    m_bookmarkManager->getAllBookmarks();
                    m_bookmarkManager->getFavoriteBookmarks();
                }
                break;
            }
        });

        threads.append(thread);
        thread->start();
    }

    // Wait for all threads to complete
    for (QThread* thread : threads) {
        thread->wait();
        delete thread;
    }

    // Verify system is still functional after concurrent access
    QVERIFY(m_bookmarkManager->getBookmarkCount() > 0);

    // Test basic operations still work
    EnhancedBookmark testBookmark = createTestBookmark("Post Concurrent Test");
    QString id = m_bookmarkManager->addBookmark(testBookmark);
    QVERIFY(!id.isEmpty());

    EnhancedBookmark retrieved = m_bookmarkManager->getBookmark(id);
    QCOMPARE(retrieved.name, QString("Post Concurrent Test"));

    // If we get here, concurrent access handling is working
    QVERIFY(true);
}

QTEST_MAIN(TestBookmarkManager)
#include "test_bookmark_manager.moc"
